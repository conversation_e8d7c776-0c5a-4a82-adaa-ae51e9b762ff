"""
MCP API abstraction for ceto_chat Django app.
Level 0033: Separate MCP API concerns
Level 0034: Self-contained MCP client implementation

Simple abstraction layer for MCP server communication.
Handles connection management and tool listing without external dependencies.
"""

import asyncio
import json
import logging
from typing import Dict, Any
from django.conf import settings
from contextlib import AsyncExitStack

# Import MCP client components
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

logger = logging.getLogger(__name__)


class MCPService:
    """
    Simple MCP service abstraction.
    Level 0034: Self-contained implementation without external dependencies.
    """

    def __init__(self):
        self.server_url = getattr(settings, 'MCP_SERVER_URL', 'http://0.0.0.0:9000/mcp')
        self.timeout = getattr(settings, 'MCP_SERVER_TIMEOUT', 30)

    async def _create_mcp_session(self) -> ClientSession:
        """
        Create an MCP client session using streamable HTTP.
        Level 0034: Proper MCP streamable HTTP connection.
        """
        exit_stack = AsyncExitStack()

        # Establish streamable HTTP transport connection
        logger.info(f"Connecting to MCP server via streamable HTTP at: {self.server_url}")
        http_transport = await exit_stack.enter_async_context(streamablehttp_client(self.server_url))
        read_stream, write_stream, _ = http_transport  # Ignore the session ID callback

        # Create and initialize the MCP ClientSession
        session = await exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
        await session.initialize()

        return session, exit_stack
    
    async def _get_tools_async(self) -> Dict[str, Any]:
        """
        Async method to get available MCP tools.
        Level 0034: MCP session-based tool listing.
        """
        try:
            session, exit_stack = await self._create_mcp_session()

            try:
                # List available tools
                tools_result = await session.list_tools()

                # Format tools for frontend
                formatted_tools = []
                for tool in tools_result.tools:
                    formatted_tools.append({
                        'name': tool.name,
                        'description': tool.description or 'No description',
                        'input_schema': tool.inputSchema or {}
                    })

                return {
                    'success': True,
                    'tools': formatted_tools,
                    'server_url': self.server_url,
                    'tool_count': len(formatted_tools)
                }

            finally:
                await exit_stack.aclose()

        except Exception as e:
            logger.error(f"Error getting MCP tools: {e}")
            return {
                'success': False,
                'error': f'MCP connection error: {str(e)}',
                'tools': [],
                'server_url': self.server_url,
                'tool_count': 0
            }

    def get_tools(self) -> Dict[str, Any]:
        """
        Get available MCP tools.
        Level 0034: Streamable HTTP-based tool listing.
        """
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self._get_tools_async())
            return result
        except Exception as e:
            logger.error(f"Error in get_tools: {e}")
            return {
                'success': False,
                'error': f'Tool listing failed: {str(e)}',
                'tools': [],
                'server_url': self.server_url,
                'tool_count': 0
            }
        finally:
            loop.close()

    async def _call_tool_async(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async method to call an MCP tool.
        Level 0034: MCP session-based tool calling.
        """
        try:
            session, exit_stack = await self._create_mcp_session()

            try:
                logger.info(f"Calling MCP tool '{tool_name}' with args: {tool_args}")

                # Call the tool using MCP session
                result = await session.call_tool(tool_name, tool_args)
                print(f"//////////////////////////// Raw tool result: {result}")

                # Check if the result indicates an error
                if result.isError:
                    error_content = ""
                    if result.content:
                        for content_item in result.content:
                            if hasattr(content_item, 'text'):
                                error_content += content_item.text

                    return {
                        'success': False,
                        'error': error_content or "Tool execution failed",
                        'result': None,
                        'tool_name': tool_name,
                        'tool_args': tool_args
                    }

                # Extract successful result content
                result_text = ""
                if result.content:
                    for content_item in result.content:
                        if hasattr(content_item, 'text'):
                            result_text += content_item.text

                return {
                    'success': True,
                    'result': result_text,
                    'tool_name': tool_name,
                    'tool_args': tool_args
                }

            finally:
                await exit_stack.aclose()

        except Exception as e:
            logger.error(f"Error calling MCP tool '{tool_name}': {e}")
            return {
                'success': False,
                'error': f'MCP tool call error: {str(e)}',
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }

    def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synchronous wrapper for calling MCP tools.
        Level 0034: Tool calling functionality
        """
        # Run async operation in new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self._call_tool_async(tool_name, tool_args))
            return result
        except Exception as e:
            logger.error(f"Error in call_tool: {e}")
            return {
                'success': False,
                'error': f'Tool call failed: {str(e)}',
                'result': None,
                'tool_name': tool_name,
                'tool_args': tool_args
            }
        finally:
            loop.close()


# Singleton instance for the app
mcp_service = MCPService()
